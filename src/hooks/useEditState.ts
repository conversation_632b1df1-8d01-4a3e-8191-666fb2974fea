import { useState, useCallback } from "react";
import type { Todo } from "../type";

export function useEditState() {
  const [editId, setEditId] = useState<number | null>(null);
  const [editTitle, setEditTitle] = useState("");

  const startEdit = useCallback((todo: Todo) => {
    setEditId(todo.id);
    setEditTitle(todo.title);
  }, []);

  const cancelEdit = useCallback(() => {
    setEditId(null);
    setEditTitle("");
  }, []);

  return {
    editId,
    editTitle,
    setEditTitle,
    startEdit,
    cancelEdit,
  };
}
