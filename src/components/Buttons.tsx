import cn from "clsx";
import { memo } from "react";

interface ButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  variant?: "primary" | "secondary" | "primary-outline" | "secondary-outline";
  className?: string;
}

const baseClasses =
  "mt-2 min-w-24 rounded-full font-medium text-sm cursor-pointer";

const variantClasses: Record<NonNullable<ButtonProps["variant"]>, string> = {
  primary: "bg-primary text-white hover:bg-primary/60 border border-primary",
  secondary:
    "bg-secondary text-white hover:bg-secondary/60 border border-secondary",
  "primary-outline":
    "bg-white border border-primary text-primary hover:bg-primary/10",
  "secondary-outline":
    "bg-white border border-secondary text-secondary hover:bg-secondary/10",
};

export const Button = memo(function Button({
  variant = "primary",
  className,
  children,
  ...props
}: Readonly<ButtonProps>) {
  return (
    <button
      className={cn(baseClasses, variantClasses[variant], className)}
      {...props}
    >
      {children}
    </button>
  );
});
