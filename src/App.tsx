import { useState, useCallback, useMemo } from "react";
import { useTodos } from "./hooks/useTodos";
import { useEditState } from "./hooks/useEditState";
import { TodoList } from "./components/TodoList";
import { AddTodoForm } from "./components/AddTodoForm";
import { LoadingSpinner } from "./components/LoadingSpinner";
import { ErrorMessage } from "./components/ErrorMessage";

function App() {
  const {
    todos,
    loading,
    error,
    addNewTodo,
    toggleTodo,
    removeTodo,
    updateTodoItem,
    clearError,
    isUpdating,
  } = useTodos();

  const { editId, editTitle, setEditTitle, startEdit, cancelEdit } =
    useEditState();

  const [newTitle, setNewTitle] = useState("");

  const handleAdd = useCallback(async () => {
    const success = await addNewTodo(newTitle);
    if (success) {
      setNewTitle("");
    }
  }, [newTitle, addNewTodo]);

  const handleEdit = useCallback(async () => {
    if (editId === null || editTitle.trim() === "") return;
    const success = await updateTodoItem(editId, editTitle);
    if (success) {
      cancelEdit();
    }
    cancelEdit();
  }, [editId, editTitle, updateTodoItem, cancelEdit]);

  const incompleteTodos = useMemo(
    () => todos.filter((todo) => !todo.completed),
    [todos]
  );

  const completedTodos = useMemo(
    () => todos.filter((todo) => todo.completed),
    [todos]
  );

  if (loading)
    return <LoadingSpinner size="lg" message="Loading your todos..." />;

  return (
    <div className="screen relative">
      <div className="w-full h-full p-8 max-w-5xl mx-auto rounded-lg shadow-lg border border-gray-200 bg-surface relative flex flex-col">
        {isUpdating && (
          <LoadingSpinner size="lg" message="Updating your todos..." />
        )}
        {error && <ErrorMessage message={error} onDismiss={clearError} />}

        <header className="w-full h-fit flex justify-between items-center">
          <h1 className="text-3xl font-bold text-text-primary mb-6">
            Todo List
          </h1>
          <AddTodoForm
            newTitle={newTitle}
            setNewTitle={setNewTitle}
            onAdd={handleAdd}
            isLoading={loading}
          />
        </header>

        <main className="w-full flex gap-4 flex-1 min-h-0">
          <section
            className="flex-1 p-8 rounded-lg shadow-lg border border-gray-200 bg-surface flex flex-col"
            aria-labelledby="active-tasks-heading"
          >
            <h2
              id="active-tasks-heading"
              className="text-xl font-semibold mb-4 text-text-primary"
            >
              Active Tasks ({incompleteTodos.length})
            </h2>
            <div className="flex-1 overflow-y-auto">
              <TodoList
                todos={incompleteTodos}
                editId={editId}
                editTitle={editTitle}
                setEditTitle={setEditTitle}
                onToggle={toggleTodo}
                onDelete={removeTodo}
                onStartEdit={startEdit}
                onSaveEdit={handleEdit}
                onCancelEdit={cancelEdit}
                showEditControls={true}
              />
            </div>
          </section>
          <section
            className="flex-1 p-8 rounded-lg shadow-lg border border-gray-200 bg-surface flex flex-col"
            aria-labelledby="completed-tasks-heading"
          >
            <h2
              id="completed-tasks-heading"
              className="text-xl font-semibold mb-4 text-text-primary"
            >
              Completed Tasks ({completedTodos.length})
            </h2>
            <div className="flex-1 overflow-y-auto">
              <TodoList
                todos={completedTodos}
                editId={editId}
                editTitle={editTitle}
                setEditTitle={setEditTitle}
                onToggle={toggleTodo}
                onDelete={removeTodo}
                onStartEdit={startEdit}
                onSaveEdit={handleEdit}
                onCancelEdit={cancelEdit}
                showEditControls={false}
              />
            </div>
          </section>
        </main>
      </div>
    </div>
  );
}

export default App;
