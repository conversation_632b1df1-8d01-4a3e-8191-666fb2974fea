# 📝 React Todo List App (TypeScript + useReducer + Axios)

This is a simple Todo List application built with **React**, **TypeScript**, and **Axios**, demonstrating basic CRUD functionality using a **mock API (JSONPlaceholder)**. State is managed cleanly using `useReducer`.

## 🚀 Features

- ✅ Fetch todos from API (JSONPlaceholder)
- ➕ Add new todo
- 🔄 Toggle Done / Not Done
- 📝 Edit todo
- ❌ Delete todo
- 🔃 Loading & Error states

---

## 📦 Tech Stack

- **React 18 + TypeScript**
- **Vite** for fast bundling
- **Axios** for API calls
- **useReducer** for state management (cleaner than useState)
- **JSONPlaceholder** mock API

---

## 🧠 Why use `useReducer`?

This app uses `useReducer` over `useState` because:

- Actions are **more than one** (add, update, delete, toggle)
- Keeps logic **centralized and scalable**
- Easier to **extend or debug** later

---

## 📁 Project Structure

src/
├── App.tsx // Main logic
├── api.ts // Axios API functions
├── reducer.ts // Todo reducer function
├── type.ts // TypeScript types

---

## ⚙️ Getting Started

### 1. C<PERSON> & Install

```bash
git clone https://github.com/yourname/my-todo-app
cd my-todo-app
npm install
```

### 2. Run

```bash
npm run dev
```

---

🛠️ API Reference (JSONPlaceholder)
• GET /todos?\_limit=10 – fetch list
• POST /todos – mock create
• PUT /todos/:id – mock update
• DELETE /todos/:id – mock delete

🔸 JSONPlaceholder does not persist changes – UI is updated optimistically.

---
