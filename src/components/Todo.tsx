import cn from "clsx";
import { Input } from "./TextInput";
import { memo } from "react";

interface TaskProps {
  isComplete: boolean;
  title: string;
  editTitle?: string;
  setEditTitle?: (title: string) => void;
  handleComplete: (id: number) => void;
  id: number;
  editId?: number | null;
}

export const Todo = memo(function Todo({
  isComplete,
  title,
  editTitle,
  setEditTitle,
  handleComplete,
  id,
  editId,
}: TaskProps) {
  const isEditing = editId === id;
  const taskId = `task-${id}`;
  const checkboxId = `checkbox-${id}`;

  return (
    <li className="flex gap-2 h-fit p-2" aria-labelledby={taskId}>
      <input
        id={checkboxId}
        type="checkbox"
        checked={isComplete}
        className="size-4 cursor-pointer mt-2"
        onChange={() => handleComplete(id)}
        aria-describedby={taskId}
        aria-label={`Mark "${title}" as ${
          isComplete ? "incomplete" : "complete"
        }`}
      />
      <p
        className={cn(
          {
            "line-through": isComplete,
            "opacity-50": isComplete,
          },
          "h-8"
        )}
      >
        {isEditing ? (
          <Input
            title={editTitle ?? ""}
            setTitle={setEditTitle ?? (() => {})}
            aria-label={`Edit todo: ${title}`}
            autoFocus
          />
        ) : (
          title
        )}
      </p>
    </li>
  );
});
