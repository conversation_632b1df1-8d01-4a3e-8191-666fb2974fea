import { memo } from "react";

interface LoadingSpinnerProps {
  size?: "sm" | "md" | "lg";
  message?: string;
}

export const LoadingSpinner = memo(function LoadingSpinner({
  size = "md",
  message = "Loading...",
}: LoadingSpinnerProps) {
  const sizeClasses = {
    sm: "w-4 h-4",
    md: "w-8 h-8",
    lg: "w-12 h-12",
  };

  return (
    <div className="h-full w-full flex flex-col items-center justify-center py-8 absolute z-50">
      <div
        className={`${sizeClasses[size]} border-8 border-gray-200 border-t-secondary rounded-full animate-spin`}
      />
      {message && <p className="mt-4 text-gray-600 text-center">{message}</p>}
    </div>
  );
});
